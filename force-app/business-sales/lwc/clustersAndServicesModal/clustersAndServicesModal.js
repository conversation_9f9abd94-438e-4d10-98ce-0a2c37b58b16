/**
 * Created by ziji<PERSON>.wang on 2024/4/24.
 */

import LightningModal from "lightning/modal";
import { api, wire, track } from "lwc";
import { createUUID } from "c/utils";
import { gql, graphql } from "lightning/uiGraphQLApi";
import Toast from "lightning/toast";
import { updateRecord } from "lightning/uiRecordApi";

const OTHER_OFFERING = "Other";
const EXCLUDED_OFFERINGS = [
    "AI",
    "Data & analytics",
    "Strategy, advisory & Transformation",
    "Customer experience & products",
    "Platforms",
    "Modernisation",
    "Cloud",
    "Security, compliance & resilience",
    "Managed services",
    "Software defined vehicles",
    "Extended reality (XR)"
];
const DAMO_OFFERING = "Digital Application Management and Ops (DAMO)";

const DAMO_PERCENT_OPTIONS = [
    {
        label: "10%",
        value: "10"
    },
    {
        label: "20%",
        value: "20"
    },
    {
        label: "30%",
        value: "30"
    },
    {
        label: "40%",
        value: "40"
    },
    {
        label: "50%",
        value: "50"
    },
    {
        label: "60%",
        value: "60"
    },
    {
        label: "70%",
        value: "70"
    },
    {
        label: "80%",
        value: "80"
    },
    {
        label: "90%",
        value: "90"
    }
];
export default class ClustersAndServicesModal extends LightningModal {
    @api recordId;
    @api isNotPersistence = false;
    @api offeringsAndServices = [];
    isLoading = false;
    showErrorIcon = false;
    isShowPopover = true;
    errorMsgHeader = "Review the following fields";
    offeringOptions = [];
    damoPercentOptions = DAMO_PERCENT_OPTIONS;
    allServiceOptions = [];
    serviceOptions = [];
    errorMsgItems = [
        {
            key: "a",
            msg: "b"
        }
    ];
    selectedOfferings = [];
    selectedDamoPercent = null;
    selectedServices = [];
    @track selectedOtherServices = [];
    offeringServiceMap = new Map();
    serviceOfferingMap = new Map();
    allServices = [];
    @api required = false;
    servicesRequired = false;
    isOtherServicesRequired = false;
    isOtherServicesDisabled = true;
    otherServicesPlaceholder = "Input other services here";
    otherServicesLabelName = "Other Services";

    get isDamoPercentEnable() {
        return this.selectedOfferings.length > 1 && this.selectedOfferings.includes(DAMO_OFFERING);
    }

    addTags(event) {
        const invalidServices = event.detail.filter((item) => this.allServices.includes(item));
        const inputPillElem = this.refs.otherServices;
        if (invalidServices.length > 0) {
            const offerings = invalidServices.map((item) => this.serviceOfferingMap.get(item));
            inputPillElem.setCustomValidity(`This service exists under ${offerings.join(",")} GTM Offering already, select the GTM Offering and Service above.`);
            inputPillElem.reportValidity();
        } else {
            inputPillElem.setCustomValidity("");
            inputPillElem.reportValidity();
        }
        const validServices = event.detail.filter((item) => !this.allServices.includes(item));
        this.selectedOtherServices = [...this.selectedOtherServices, ...Array.from(new Set(validServices))];
    }

    deleteTags(event) {
        this.selectedOtherServices = this.selectedOtherServices.filter((item) => item !== event.detail);
    }

    setOtherServices() {
        let isOfferingsIncludeOthers = this.selectedOfferings.includes(OTHER_OFFERING);
        this.isOtherServicesRequired = isOfferingsIncludeOthers;
        this.isOtherServicesDisabled = !isOfferingsIncludeOthers;

        if (!isOfferingsIncludeOthers) {
            this.selectedOtherServices = [];
        }
    }

    @wire(graphql, {
        query: gql`
            query FetchClustersAndServicesMappings {
                uiapi {
                    query {
                        GTM_Cluster_Service_Mapping__c(first: 2000, upperBound: 5000) {
                            edges {
                                node {
                                    Id
                                    Cluster_Name__c {
                                        value
                                    }
                                    Service_Name__c {
                                        value
                                    }
                                    Definition__c {
                                        value
                                    }
                                }
                            }
                        }
                    }
                }
            }
        `
    })
    graphqlQueryResult({ errors, data }) {
        if (data) {
            const results = data.uiapi.query.GTM_Cluster_Service_Mapping__c.edges;

            results
                .filter(({ node }) => !EXCLUDED_OFFERINGS.includes(node.Cluster_Name__c.value))
                .forEach(({ node }) => {
                    const offeringName = node.Cluster_Name__c.value;
                    const serviceName = node.Service_Name__c.value;
                    const helpText = node.Definition__c.value;
                    const offeringList = [...(this.serviceOfferingMap.get(serviceName) || []), offeringName];
                    this.serviceOfferingMap.set(serviceName, offeringList);
                    if (!this.offeringServiceMap.has(offeringName)) {
                        this.offeringServiceMap.set(offeringName, []);
                    }
                    this.offeringServiceMap.get(offeringName).push({ label: serviceName, value: serviceName, helpText });
                    this.allServices.push(serviceName);
                });
            this.offeringOptions = this.formatOfferingSelectOptions();
            this.allServiceOptions = this.formatServiceSelectOptions();

            this.showExistData();
        } else if (errors) {
            console.error("Error fetching data:", errors);
        }
    }

    showExistData() {
        const value = this.offeringsAndServices || [];
        this.selectedOfferings = value.map((it) => it.offering);
        this.selectedOtherServices = value.filter((it) => it.offering === OTHER_OFFERING).flatMap((it) => it.services);
        this.selectedServices = value.filter((it) => it.offering !== OTHER_OFFERING).flatMap((it) => it.services);
        this.selectedDamoPercent = (this.isDamoPercentEnable && value.find((it) => it.offering === DAMO_OFFERING).proportion?.toString()) || null;

        this.updateServiceOptionsByOfferings();
        this.setOtherServices();
        this.setServiceRequired();
    }

    handleClickSave() {
        if (!this.validateOfferingsAndServices()) {
            return;
        }
        const structureOfferingAndServices = this.prepareStructureOfferingAndService(this.selectedOfferings, this.selectedServices, this.selectedOtherServices);
        if (this.isNotPersistence) {
            this.close({ type: "done", detail: structureOfferingAndServices });
        } else {
            const offeringsAndServices = this.prepareOfferingAndService();
            this.updateOfferingAndService(offeringsAndServices, structureOfferingAndServices);
        }
    }

    prepareOfferingAndService() {
        const isIncludeDamo = this.selectedOfferings.includes(DAMO_OFFERING);
        const offeringCount = this.selectedOfferings.length;
        const allSelectedService = [...this.selectedServices, ...this.selectedOtherServices];
        let damoProportion;
        if (isIncludeDamo && offeringCount === 1) {
            damoProportion = 100;
        } else if (isIncludeDamo && offeringCount > 1) {
            damoProportion = this.selectedDamoPercent;
        } else {
            damoProportion = 0;
        }
        return {
            offerings: this.selectedOfferings.join(";"),
            services: allSelectedService.join(";"),
            damoProportion
        };
    }

    prepareStructureOfferingAndService(selectedOfferings, selectedServices, selectedOtherServices) {
        const isIncludeDamo = selectedOfferings.includes(DAMO_OFFERING);
        const offeringCount = selectedOfferings.length;
        return selectedOfferings.map((offering) => {
            let services;
            if (offering === OTHER_OFFERING) {
                services = selectedOtherServices;
            } else {
                services = selectedServices.filter((service) => (this.offeringServiceMap.get(offering) || []).map((i) => i.value).includes(service));
            }
            let proportion;
            if (offeringCount === 1) {
                proportion = 100;
            } else if (isIncludeDamo && offering === DAMO_OFFERING) {
                proportion = Number(this.selectedDamoPercent);
            } else if (isIncludeDamo && offering !== DAMO_OFFERING) {
                proportion = Number(((100 - this.selectedDamoPercent) / (offeringCount - 1)).toFixed(2));
            } else {
                proportion = Number((100 / offeringCount).toFixed(2));
            }
            return {
                offering,
                services,
                proportion
            };
        });
    }

    updateOfferingAndService(offeringsAndServices, structureOfferingAndServices) {
        this.isLoading = true;
        const record = {
            fields: {
                Id: this.recordId,
                Cluster__c: offeringsAndServices.offerings,
                Services__c: offeringsAndServices.services,
                Damo_Proportion__c: offeringsAndServices.damoProportion
            }
        };
        updateRecord(record)
            .then(() => {
                this.close({ type: "done", detail: structureOfferingAndServices });
            })
            .catch((error) => {
                console.error("Error updating offerings and services for opportunity: ", error);
                Toast.show({
                    label: "Can't save this opportunity",
                    variant: "error",
                    mode: "dismissible"
                });
            })
            .finally(() => {
                this.isLoading = false;
            });
    }

    formatOfferingSelectOptions() {
        if (this.offeringServiceMap.size === 0) {
            return [];
        }
        return Array.from(this.offeringServiceMap.keys())
            .sort((a, b) => {
                if (a === OTHER_OFFERING) return 1; // "Other" at the end
                if (b === OTHER_OFFERING) return -1;
                return a.localeCompare(b); // Sort alphabetically
            })
            .map((offeringName) => ({
                label: offeringName,
                value: offeringName
            }));
    }

    formatServiceSelectOptions() {
        if (this.offeringServiceMap.size === 0) {
            return [];
        }

        return Array.from(this.offeringServiceMap.keys())
            .filter((it) => it !== OTHER_OFFERING)
            .sort((a, b) => a.localeCompare(b))
            .map((offeringName) => ({
                label: offeringName,
                value: offeringName,
                isParent: true,
                children: this.offeringServiceMap.get(offeringName)
            }));
    }

    handleClickCancel() {
        this.close({ type: "cancel" });
    }

    updateServiceOptionsByOfferings() {
        const selectedOfferings = this.selectedOfferings;
        this.serviceOptions = this.allServiceOptions.filter((option) => selectedOfferings.includes(option.value));
    }

    updateSelectedServiceByOffering() {
        const serviceBySelectedOfferings = this.selectedOfferings.flatMap((offering) => this.offeringServiceMap.get(offering) || []) || [];
        const services = new Set(serviceBySelectedOfferings.map((item) => item.value));
        this.selectedServices = this.selectedServices.filter((service) => services.has(service));
    }

    setServiceRequired() {
        if (this.selectedOfferings.length === 1 && this.selectedOfferings[0] === OTHER_OFFERING) {
            this.servicesRequired = false;
        } else {
            this.servicesRequired = this.required;
        }
    }

    handleOfferingsChange(event) {
        this.selectedOfferings = event.detail.value;
        this.setServiceRequired();
        this.setOtherServices();
        this.updateServiceOptionsByOfferings();
        this.updateSelectedServiceByOffering();
    }

    handleDamoPercentChange(event) {
        this.selectedDamoPercent = event.detail.value;
    }

    handleServicesChange(event) {
        this.selectedServices = event.detail.value;
    }

    handleOfferingTagDelete(event) {
        this.selectedOfferings = event.detail.selectedValue;
        this.setServiceRequired();
        this.setOtherServices();
        this.updateServiceOptionsByOfferings();
        this.updateSelectedServiceByOffering();
    }

    handleServiceTagDelete(event) {
        this.selectedServices = event.detail.selectedValue;
    }

    handleClickErrorIcon() {
        this.isShowPopover = !this.isShowPopover;
    }

    handleClickCloseIcon() {
        this.isShowPopover = false;
    }

    offeringMustHasService() {
        return this.selectedOfferings.filter((c) => c !== OTHER_OFFERING).every((c) => this.offeringServiceMap.get(c).some((item) => this.selectedServices.includes(item.value)));
    }

    validateOfferingsAndServices() {
        this.errorMsgItems = [];
        console.log(this.refs.offerings);
       
        
        let isOfferingsValid = this.refs.offerings.reportValidity();
        let isDamoPercentValid = !this.isDamoPercentEnable || this.refs.damoPercent.reportValidity();
        let isServicesValid = this.refs.services.reportValidity();
        let isOtherServicesValid = this.refs.otherServices.reportValidity();
        if (!isOfferingsValid) {
            this.errorMsgItems.push({
                key: createUUID(),
                msg: "GTM Offerings"
            });
        }
        if (!isDamoPercentValid) {
            this.errorMsgItems.push({
                key: createUUID(),
                msg: "DAMO %"
            });
        }
        if (!isServicesValid) {
            this.errorMsgItems.push({
                key: createUUID(),
                msg: "Services"
            });
        }
        if (!isOtherServicesValid) {
            this.errorMsgItems.push({
                key: createUUID(),
                msg: "Other Services"
            });
        }
        const offeringHasOneService = this.offeringMustHasService();
        if (!offeringHasOneService) {
            this.errorMsgItems.push({
                key: createUUID(),
                msg: "At least 1 service under selected GTM Offering should be selected."
            });
        }
        this.showErrorIcon = !isOfferingsValid || !isDamoPercentValid || !isServicesValid || !isOtherServicesValid || !offeringHasOneService;
        return isOfferingsValid && isDamoPercentValid && isServicesValid && isOtherServicesValid && offeringHasOneService;
    }
}
